"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel"
import { getEventInitials, generateEventGradient } from "@/lib/utils"

interface EventImage {
  url: string
  alt_text?: string
  order?: number
  is_primary?: boolean
}

interface EventImageSliderProps {
  images?: EventImage[]
  eventTitle: string
  className?: string
}

export function EventImageSlider({
  images = [],
  eventTitle,
  className
}: EventImageSliderProps) {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())
  const [currentIndex, setCurrentIndex] = useState(0)
  const [api, setApi] = useState<CarouselApi>()

  // Prepare image list - use images array only
  const imageList = images.length > 0
    ? images.sort((a, b) => (a.order || 0) - (b.order || 0))
    : []

  const handleImageError = (imageUrl: string) => {
    setImageErrors(prev => new Set([...prev, imageUrl]))
  }

  useEffect(() => {
    if (!api) {
      return
    }

    const onSelect = () => {
      setCurrentIndex(api.selectedScrollSnap())
    }

    api.on("select", onSelect)
    onSelect()

    return () => {
      api.off("select", onSelect)
    }
  }, [api])

  const renderFallbackImage = () => {
    const gradient = generateEventGradient(eventTitle)
    return (
      <div
        className={`w-full h-full ${gradient.className} flex items-center justify-center`}
        style={gradient.style}
      >
        <span className="text-white font-bold text-4xl md:text-6xl">
          {getEventInitials(eventTitle)}
        </span>
      </div>
    )
  }

  const renderImage = (image: EventImage, index: number) => {
    const hasError = imageErrors.has(image.url)

    if (hasError) {
      return renderFallbackImage()
    }

    return (
      <Image
        src={image.url}
        alt={image.alt_text || `${eventTitle} - Image ${index + 1}`}
        fill
        className="object-cover"
        priority={index === 0}
        onError={() => handleImageError(image.url)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
      />
    )
  }

  // If no images at all, show fallback
  if (imageList.length === 0) {
    return (
      <div className={`relative h-64 md:h-96 w-full bg-gray-200 ${className}`}>
        {renderFallbackImage()}
      </div>
    )
  }

  // If only one image, show simple image without carousel
  if (imageList.length === 1) {
    return (
      <div className={`relative h-64 md:h-96 w-full bg-gray-200 ${className}`}>
        {renderImage(imageList[0], 0)}
      </div>
    )
  }

  // Multiple images - show carousel
  return (
    <div className={`relative ${className}`}>
      <Carousel className="w-full" setApi={setApi}>
        <CarouselContent>
          {imageList.map((image, index) => (
            <CarouselItem key={index}>
              <div className="relative h-64 md:h-96 w-full bg-gray-200">
                {renderImage(image, index)}
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>

        {/* Custom navigation buttons */}
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none" />
      </Carousel>

      {/* Image indicators */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {imageList.map((_, index) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full transition-all ${
              index === currentIndex
                ? 'bg-white'
                : 'bg-white/50 hover:bg-white/75'
            }`}
            onClick={() => api?.scrollTo(index)}
            aria-label={`Go to image ${index + 1}`}
          />
        ))}
      </div>

      {/* Image counter */}
      <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
        {currentIndex + 1} / {imageList.length}
      </div>
    </div>
  )
}
