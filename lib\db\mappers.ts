/**
 * Database to Application Schema Mappers
 *
 * This file contains functions to map between the database schema and the application schema.
 * Use these functions when fetching data from or sending data to the database.
 */

import { Event as DbEvent } from "./supabase-schema";
import { EventType } from "@/contexts/event-context";

/**
 * Maps a database event to an application event
 */
export function mapDbEventToAppEvent(dbEvent: DbEvent): EventType {
  return {
    id: dbEvent.id,
    title: dbEvent.title,
    slug: dbEvent.slug,
    description: dbEvent.description || "",
    description_html: dbEvent.description_html || "",
    location: dbEvent.location || "",
    start_date: dbEvent.start_date,
    end_date: dbEvent.end_date,
    price: dbEvent.price,
    max_participants: dbEvent.max_participants,
    current_participants: 0, // This is calculated or fetched separately
    images: dbEvent.images || [],
    status: dbEvent.is_published ? "published" : "draft",
    created_by: dbEvent.organizer_id,
    created_at: dbEvent.created_at,
    updated_at: dbEvent.updated_at,
    payment_gateway_id: null, // Not in DB schema
    category_id: dbEvent.category_id || null,
    is_featured: false, // Not in DB schema
    enable_certificates: false, // Not in DB schema
    enable_attendance: false, // Not in DB schema
    is_public: dbEvent.is_published // Use is_published as a proxy
  };
}

/**
 * Maps an application event to a database event
 */
export function mapAppEventToDbEvent(appEvent: EventType): Partial<DbEvent> {
  return {
    id: appEvent.id,
    title: appEvent.title,
    slug: appEvent.slug,
    description: appEvent.description,
    description_html: appEvent.description_html,
    location: appEvent.location,
    start_date: appEvent.start_date,
    end_date: appEvent.end_date,
    image_url: appEvent.image_url,
    organizer_id: appEvent.created_by,
    max_participants: appEvent.max_participants,
    price: appEvent.price,
    is_published: appEvent.status === "published",
    created_at: appEvent.created_at,
    updated_at: appEvent.updated_at
  };
}