/**
 * Supabase Database Schema Reference
 *
 * This file contains TypeScript interfaces that match the actual Supabase database schema.
 * Use these types when interacting with the database to ensure type safety.
 */

/**
 * Event Categories Table
 * Stores event category definitions
 */
export interface EventCategory {
  id: string; // UUID, Primary Key
  name: string; // Text, NOT NULL, UNIQUE
  description: string | null; // Text
  color: string; // Text, DEFAULT '#6366f1'
  icon: string | null; // Text
  is_active: boolean; // Boolean, DEFAULT true
  created_at: string; // Timestamp with time zone, DEFAULT now()
  updated_at: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Ticket Type Interface
 * Defines the structure of individual ticket types
 */
export interface TicketType {
  id: string; // Unique identifier for the ticket type
  name: string; // Display name of the ticket
  description: string; // Description of what the ticket includes
  price: number; // Price in the event's currency
  maxQuantity: number; // Maximum quantity per purchase
  availableQuantity: number; // Available quantity for sale
  features: string[]; // Array of features/benefits included
  isPopular?: boolean; // Whether this ticket type is marked as popular
  saleStartDate?: string; // When this ticket type becomes available for sale
  saleEndDate?: string; // When this ticket type stops being available for sale (deadline)
}

/**
 * Custom Field Interface
 * Defines the structure of custom fields for event registration
 */
export interface CustomField {
  id: string; // Unique identifier for the field
  label: string; // Display label for the field
  type: 'text' | 'email' | 'phone' | 'number' | 'select' | 'checkbox' | 'textarea'; // Field type
  required: boolean; // Whether the field is required
  placeholder?: string; // Placeholder text
  options?: string[]; // Options for select fields
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string; // Regex pattern for validation
  };
  order: number; // Display order
}

/**
 * Event Custom Fields Table
 * Stores reusable custom field templates
 */
export interface EventCustomField {
  id: number; // BIGINT, Primary Key
  created_at: string; // Timestamp with time zone
  updated_at: string; // Timestamp with time zone
  created_by: string | null; // UUID (references users.id)
  name: string; // Display name for the template
  description: string | null; // Description of the field
  field_details: CustomField; // JSONB containing the field configuration
  is_active: boolean; // Whether this template is available for use
  usage_count: number; // Number of times this field has been used
}

/**
 * Event Ticket Interface
 * Defines the structure of tickets in the events table
 */
export interface EventTicket {
  ticketType: TicketType; // The ticket type configuration
  quantity: number; // Default/suggested quantity
}

/**
 * Events Table
 * Stores information about events created in the system
 */
export interface Event {
  id: string; // UUID, Primary Key
  title: string; // Text, NOT NULL
  slug: string; // Text, NOT NULL, UNIQUE
  description: string; // Text
  description_html?: string; // Text, Rich HTML content for WYSIWYG
  short_description?: string; // Text
  location: string; // Text
  venue_details?: any; // JSONB
  start_date: string; // Timestamp with time zone
  end_date: string; // Timestamp with time zone
  registration_deadline?: string; // Timestamp with time zone
  images?: any[]; // JSONB array of images
  event_manager_id: string; // UUID, NOT NULL (references users.id)
  organization_id: string | null; // UUID (references organizations.id)
  max_participants: number | null; // Integer
  price: number | null; // Numeric, DEFAULT 0 (legacy field, use tickets instead)
  is_published: boolean; // Boolean, DEFAULT false
  category_id: string | null; // UUID (references event_categories.id)
  category: string | null; // Text (for backward compatibility)
  is_featured: boolean; // Boolean, DEFAULT false
  enable_certificates: boolean; // Boolean, DEFAULT false
  enable_attendance: boolean; // Boolean, DEFAULT false
  tickets: EventTicket[]; // JSONB array of ticket configurations
  custom_fields: CustomField[]; // JSONB array of custom field configurations
  created_at: string; // Timestamp with time zone, DEFAULT now()
  updated_at: string; // Timestamp with time zone, DEFAULT now()
  organizer?: {
    id: string;
    name: string;
  }; // Optional organizer information when joined
}

/**
 * Registrations Table
 * Stores information about event registrations
 */
export interface Registration {
  id: string; // UUID, Primary Key
  event_id: string; // UUID, NOT NULL (references events.id)
  user_id?: string; // UUID (references users.id)
  created_by?: string; // UUID (references users.id) - for group registrations
  attendee_name: string; // Text, NOT NULL
  attendee_email: string; // Text, NOT NULL
  attendee_phone?: string; // Text
  ic_reg?: string; // Text, IC/Registration number
  payment_status: string; // Text, DEFAULT 'pending'
  payment_amount: number; // Numeric
  payment_date?: string; // Timestamp with time zone
  transaction_id?: string; // UUID (references transactions.id)
  group_registration_id?: string; // UUID - Groups multiple registrations created together
  checked_in: boolean; // Boolean, DEFAULT false
  checked_in_at?: string; // Timestamp with time zone
  certificate_issued: boolean; // Boolean, DEFAULT false
  certificate_id?: string; // UUID (references certificates.id)
  status: string; // Text, DEFAULT 'registered'
  participants?: any[]; // JSONB array for group registrations
  custom_field_responses: Record<string, any>; // JSONB object storing custom field responses
  payment_callback_data?: Record<string, any>; // JSONB object storing payment gateway callback data
  created_at: string; // Timestamp with time zone, DEFAULT now()
  updated_at: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Certificates Table
 * Stores information about certificates issued to event attendees
 */
export interface Certificate {
  id: string; // UUID, Primary Key
  registration_id: string; // UUID, NOT NULL (references registrations.id)
  event_id: string; // UUID, NOT NULL (references events.id)
  template_id: string; // Text, NOT NULL
  certificate_url?: string; // Text
  issued_at?: string; // Timestamp with time zone, DEFAULT now()
  created_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * System Settings Table
 * Stores global system configuration
 */
export interface SystemSettings {
  id: string; // UUID, Primary Key
  event_fee_percentage: number; // Numeric, NOT NULL, DEFAULT 5.00
  withdrawal_fee_percentage: number; // Numeric, NOT NULL, DEFAULT 1.50
  min_withdrawal_amount: number; // Numeric, NOT NULL, DEFAULT 50.00
  payment_gateways: PaymentGateway[]; // JSONB, NOT NULL
  certificate_templates: CertificateTemplate[]; // JSONB, NOT NULL
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_by?: string; // UUID (references users.id)
}

/**
 * App Settings Table
 * Stores admin configuration for application features
 */
export interface AppSettings {
  id: string; // UUID, Primary Key
  login_enabled: boolean; // Boolean, DEFAULT true
  register_enabled: boolean; // Boolean, DEFAULT true
  password_reset_enabled: boolean; // Boolean, DEFAULT true
  api_enabled: boolean; // Boolean, DEFAULT true
  maintenance_mode: boolean; // Boolean, DEFAULT false
  maintenance_message: string; // Text, DEFAULT message
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_by?: string; // UUID (references users.id)
}

/**
 * Roles Table
 * Stores role definitions and permissions
 */
export interface Role {
  id: string; // UUID, Primary Key
  role_name: string; // Text, NOT NULL, UNIQUE
  description?: string; // Text
  parent_role_id?: string; // UUID, references roles.id
  permissions?: Record<string, any>; // JSONB, DEFAULT '{}'
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Users Table
 * Stores user information
 */
export interface User {
  id: string; // UUID, Primary Key
  email: string; // Text, NOT NULL
  full_name?: string; // Text
  role?: string; // Text, DEFAULT 'free' (legacy field, use role_id instead)
  role_id?: string; // UUID (references roles.id)
  subscription_status?: string; // Text, DEFAULT 'none'
  subscription_end_date?: string; // Timestamp without time zone
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  organization?: string; // Text
  profile_image_url?: string; // Text
  phone?: string; // VARCHAR(20)
  bio?: string; // Text
  events_created?: number; // Integer, DEFAULT 0
  total_earnings?: number; // Numeric, DEFAULT 0
  available_balance?: number; // Numeric, DEFAULT 0
  organization_id?: string; // UUID (references organizations.id)
}

/**
 * Activity Logs Table
 * Stores system activity logs
 */
export interface ActivityLog {
  id: string; // UUID, Primary Key
  user_id?: string; // UUID (references users.id)
  event_id?: string; // UUID (references events.id)
  action: string; // Text, NOT NULL
  details?: Record<string, any>; // JSONB
  created_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Transactions Table
 * Unified table for all financial transaction records including payments, refunds, withdrawals, and other monetary operations
 */
export interface Transaction {
  id: string; // UUID, Primary Key
  user_id?: string; // UUID (references users.id)
  registration_id?: string; // UUID (references registrations.id)
  subscription_id?: string; // UUID (references user_subscription.id)
  gateway_id?: string; // UUID (references payment_gateway_settings.id)
  event_id?: string; // UUID (references events.id) - Direct event association
  transaction_type: string; // Text, NOT NULL, DEFAULT 'registration_payment'
  amount: number; // Numeric, NOT NULL
  fee_amount?: number; // Numeric, DEFAULT 0 - Transaction fees
  net_amount?: number; // Numeric - Net amount after fees (amount - fee_amount)
  currency: string; // Text, DEFAULT 'MYR'
  status: string; // Text, DEFAULT 'pending' (pending, processing, paid, failed, refunded)
  gateway_transaction_id?: string; // Text - Transaction ID from payment gateway
  gateway_response?: Record<string, any>; // JSONB - Response from payment gateway
  invoice_number?: string; // Text - Auto-generated invoice number (INV1000, INV1001, etc.)
  receipt_number?: string; // Text - Auto-generated receipt number when paid (RCP1000, RCP1001, etc.)
  group_transaction_id?: string; // UUID - Links multiple registrations in a group payment
  payment_method?: string; // Text - Payment method used (online_banking, credit_card, etc.)
  processed_at?: string; // Timestamp with time zone - When payment was processed
  created_at: string; // Timestamp with time zone, DEFAULT now()
  updated_at: string; // Timestamp with time zone, DEFAULT now()
  metadata?: Record<string, any>; // JSONB - Additional transaction metadata
}

/**
 * Legacy Financial Transaction interface for backward compatibility
 * @deprecated Use Transaction interface instead - this table has been merged into transactions
 * This interface is kept only for any remaining legacy code references
 */
export interface FinancialTransaction {
  id: string; // UUID, Primary Key
  user_id: string; // UUID, NOT NULL (references users.id)
  event_id?: string; // UUID (references events.id)
  registration_id?: string; // UUID (references registrations.id)
  transaction_type: string; // Text, NOT NULL
  amount: number; // Numeric, NOT NULL
  fee_amount?: number; // Numeric, DEFAULT 0
  status: string; // Text, NOT NULL, DEFAULT 'pending'
  payment_method?: string; // Text
  payment_id?: string; // Text
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Organizations Table
 * Stores organization information
 */
export interface Organization {
  id: string; // UUID, Primary Key
  name: string; // Character varying, NOT NULL
  ssm_number: string; // Character varying, NOT NULL
  pic_name: string; // Character varying, NOT NULL
  pic_phone: string; // Character varying, NOT NULL
  pic_email: string; // Character varying, NOT NULL
  logo_url?: string; // Text
  address?: string; // Text
  city?: string; // Character varying
  state?: string; // Character varying
  postal_code?: string; // Character varying
  country?: string; // Character varying, DEFAULT 'Malaysia'
  website?: string; // Character varying
  created_by: string; // UUID, NOT NULL (references users.id)
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * User Subscriptions Table
 * Stores user subscription information
 */
export interface UserSubscription {
  id: string; // UUID, Primary Key
  user_id: string; // UUID, NOT NULL (references users.id)
  subscription_type: string; // Text, NOT NULL, DEFAULT 'free'
  start_date?: string; // Timestamp with time zone, DEFAULT now()
  end_date?: string; // Timestamp with time zone
  is_active?: boolean; // Boolean, DEFAULT true
  payment_id?: string; // Text
  created_at?: string; // Timestamp with time zone, DEFAULT now()
  updated_at?: string; // Timestamp with time zone, DEFAULT now()
}

/**
 * Payment Gateway Type
 * Used in SystemSettings.payment_gateways
 */
export interface PaymentGateway {
  name: string;
  enabled: boolean;
  [key: string]: any; // Additional configuration fields
}

/**
 * Certificate Template Type
 * Used in SystemSettings.certificate_templates
 */
export interface CertificateTemplate {
  id: string;
  name: string;
  is_premium: boolean;
  thumbnail_url: string;
  [key: string]: any; // Additional template fields
}

/**
 * Webhooks Table
 * Stores webhook configurations for event notifications
 */
export interface Webhook {
  id: string; // UUID, Primary Key
  name: string; // Text, NOT NULL
  url: string; // Text, NOT NULL
  events: string[]; // JSONB, NOT NULL - Array of event types
  active: boolean; // Boolean, DEFAULT true
  created_at: string; // Timestamp with time zone, DEFAULT now()
  last_triggered_at?: string; // Timestamp with time zone
  success_count: number; // Integer, DEFAULT 0
  failure_count: number; // Integer, DEFAULT 0
  user_id: string; // UUID, NOT NULL (references users.id)
}

/**
 * API Keys Table
 * Stores API keys for API authentication
 */
export interface ApiKey {
  id: string; // UUID, Primary Key
  key: string; // Text, UNIQUE, NOT NULL
  user_id: string; // UUID, NOT NULL (references users.id)
  created_at: string; // Timestamp with time zone, DEFAULT now()
  last_used_at?: string; // Timestamp with time zone
}