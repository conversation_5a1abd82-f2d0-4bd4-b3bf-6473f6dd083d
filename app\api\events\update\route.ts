import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken } from "@/lib/auth"

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid authentication token" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { eventId, eventData, updatedBy } = body

    if (!eventId || !eventData) {
      return NextResponse.json(
        { error: "Event ID and event data are required" },
        { status: 400 }
      )
    }

    // Get Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Get the original event data for logging changes
    const { data: originalEvent, error: fetchError } = await supabaseAdmin
      .from("events")
      .select("*")
      .eq("id", eventId)
      .single()

    if (fetchError) {
      console.error("Error fetching original event:", fetchError)
      return NextResponse.json(
        { error: `Failed to fetch original event: ${fetchError.message}` },
        { status: 500 }
      )
    }

    // Check if user has permission to update this event
    const userRole = authResult.user.user_roles?.role_name || authResult.user.role
    const isEventManager = originalEvent.event_manager_id === authResult.user.id
    const isAdmin = userRole === 'admin'

    if (!isEventManager && !isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to update this event" },
        { status: 403 }
      )
    }

    // Clean and map the eventData to match database schema
    const cleanEventData: any = {}

    // Core fields that should always exist
    if (eventData.title !== undefined && eventData.title !== null) cleanEventData.title = eventData.title
    if (eventData.location !== undefined && eventData.location !== null) cleanEventData.location = eventData.location
    if (eventData.start_date !== undefined && eventData.start_date !== null) cleanEventData.start_date = eventData.start_date
    if (eventData.end_date !== undefined && eventData.end_date !== null) cleanEventData.end_date = eventData.end_date
    if (eventData.price !== undefined) cleanEventData.price = eventData.price
    if (eventData.max_participants !== undefined) cleanEventData.max_participants = eventData.max_participants
    if (eventData.category_id !== undefined) cleanEventData.category_id = eventData.category_id
    if (eventData.organization_id !== undefined && eventData.organization_id !== null) cleanEventData.organization_id = eventData.organization_id
    if (eventData.is_featured !== undefined) cleanEventData.is_featured = eventData.is_featured

    // Description fields - use description_html instead of description since description column doesn't exist
    if (eventData.description_html !== undefined && eventData.description_html !== null) {
      cleanEventData.description_html = eventData.description_html
    } else if (eventData.description !== undefined && eventData.description !== null) {
      // If only plain description is provided, store it in description_html
      cleanEventData.description_html = eventData.description
    }

    // Optional fields that may not exist in all database versions
    if (eventData.venue_details !== undefined) cleanEventData.venue_details = eventData.venue_details
    if (eventData.registration_deadline !== undefined) cleanEventData.registration_deadline = eventData.registration_deadline
    if (eventData.images !== undefined) cleanEventData.images = eventData.images
    if (eventData.tickets !== undefined) cleanEventData.tickets = eventData.tickets
    if (eventData.enable_certificates !== undefined) cleanEventData.enable_certificates = eventData.enable_certificates
    if (eventData.enable_attendance !== undefined) cleanEventData.enable_attendance = eventData.enable_attendance
    if (eventData.is_public !== undefined) cleanEventData.is_public = eventData.is_public
    if (eventData.custom_fields !== undefined) cleanEventData.custom_fields = eventData.custom_fields

    // Map status field directly (the database has a status column)
    if (eventData.status !== undefined && eventData.status !== null) {
      cleanEventData.status = eventData.status
      // Also update is_published for backward compatibility
      cleanEventData.is_published = eventData.status === 'published'
    }

    // Map created_by to event_manager_id (for both create and update)
    if (eventData.created_by !== undefined && eventData.created_by !== null) {
      cleanEventData.event_manager_id = eventData.created_by
    }

    // Add updated_by field if provided
    if (updatedBy && updatedBy !== null) {
      cleanEventData.updated_by = updatedBy
    }

    // Validate that we have data to update
    if (Object.keys(cleanEventData).length === 0) {
      return NextResponse.json(
        { error: "No valid data provided for update" },
        { status: 400 }
      )
    }

    // Add updated_at timestamp
    cleanEventData.updated_at = new Date().toISOString()

    console.log("API: Final data being sent to Supabase:", cleanEventData)
    console.log("API: Event ID for update:", eventId)

    // Update the event
    const { data, error } = await supabaseAdmin
      .from("events")
      .update(cleanEventData)
      .eq("id", eventId)
      .select()

    if (error) {
      console.error("Supabase update error:", error)
      return NextResponse.json(
        { error: `Failed to update event: ${error.message}` },
        { status: 500 }
      )
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "No rows were updated" },
        { status: 500 }
      )
    }

    const updatedEvent = data[0]

    // Log the event update activity
    try {
      const fieldsUpdated = Object.keys(eventData)
      const previousValues: Record<string, any> = {}
      if (originalEvent) {
        fieldsUpdated.forEach(field => {
          previousValues[field] = originalEvent[field]
        })
      }

      await supabaseAdmin.from("activity_logs").insert([
        {
          user_id: authResult.user.id,
          action: "update_event",
          entity_type: "event",
          entity_id: eventId,
          category: "event",
          details: {
            event_name: updatedEvent.title,
            fields_updated: fieldsUpdated,
            previous_values: previousValues,
            new_values: fieldsUpdated.reduce((acc, field) => {
              acc[field] = eventData[field]
              return acc
            }, {} as Record<string, any>),
            status_change: eventData.status !== originalEvent?.status
              ? `${originalEvent?.status} → ${eventData.status}`
              : undefined,
          },
          created_at: new Date().toISOString(),
        },
      ])
    } catch (logError) {
      // Don't fail event update if logging fails
      console.error("Error logging event update activity:", logError)
    }

    return NextResponse.json({
      success: true,
      event: updatedEvent
    })

  } catch (error: any) {
    console.error("Error in event update API:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
