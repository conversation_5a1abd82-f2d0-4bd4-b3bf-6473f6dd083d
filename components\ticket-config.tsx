"use client"

import { useState } from "react"
import { useForm<PERSON>ontext, useFieldArray } from "react-hook-form"
import { Plus, Trash2, Star, Calendar, DollarSign, Users, Tag } from "lucide-react"
import { v4 as uuidv4 } from "uuid"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form"

interface TicketConfigProps {
  name: string
}

export function TicketConfig({ name }: TicketConfigProps) {
  const { control, watch, setValue } = useFormContext()
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  })

  const addTicket = () => {
    const newTicket = {
      ticketType: {
        id: uuidv4(),
        name: "",
        description: "",
        price: 0,
        maxQuantity: 10,
        availableQuantity: 100,
        features: [],
        isPopular: false,
        saleStartDate: "",
        saleEndDate: "",
      },
      quantity: 1,
    }
    append(newTicket)
  }

  const addFeature = (ticketIndex: number, feature: string) => {
    if (!feature.trim()) return

    const currentTicket = watch(`${name}.${ticketIndex}`)
    const updatedFeatures = [...(currentTicket.ticketType.features || []), feature.trim()]

    // Update the features array in the form
    setValue(`${name}.${ticketIndex}.ticketType.features`, updatedFeatures)
  }

  const removeFeature = (ticketIndex: number, featureIndex: number) => {
    const currentTicket = watch(`${name}.${ticketIndex}`)
    const updatedFeatures = currentTicket.ticketType.features.filter((_: string, index: number) => index !== featureIndex)

    // Update the features array in the form
    setValue(`${name}.${ticketIndex}.ticketType.features`, updatedFeatures)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Ticket Configuration</h3>
          <p className="text-sm text-muted-foreground">
            Configure different ticket types for your event
          </p>
        </div>
        <Button type="button" onClick={addTicket} variant="outline" size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Ticket Type
        </Button>
      </div>

      {fields.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Tag className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Ticket Types</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Add ticket types to allow attendees to register for your event.
            </p>
            <Button type="button" onClick={addTicket} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Ticket Type
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="space-y-4">
        {fields.map((field, index) => (
          <Card key={field.id} className="relative">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Ticket Type {index + 1}
                </CardTitle>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => remove(index)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={control}
                  name={`${name}.${index}.ticketType.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ticket Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Early Bird, VIP, Standard" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name={`${name}.${index}.ticketType.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price (RM) *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            placeholder="0.00"
                            className="pl-10"
                            {...field}
                            value={field.value ? field.value.toFixed(2) : "0.00"}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              field.onChange(parseFloat(value.toFixed(2)))
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={control}
                name={`${name}.${index}.ticketType.description`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what this ticket includes..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={control}
                  name={`${name}.${index}.ticketType.maxQuantity`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Quantity per Purchase</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            min="1"
                            placeholder="10"
                            className="pl-10"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>Maximum tickets per single purchase</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name={`${name}.${index}.ticketType.availableQuantity`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Available Quantity</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            min="0"
                            placeholder="100"
                            className="pl-10"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>Total tickets available for sale</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={control}
                name={`${name}.${index}.ticketType.isPopular`}
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="flex items-center gap-2">
                        <Star className="h-4 w-4" />
                        Mark as Popular
                      </FormLabel>
                      <FormDescription>
                        Popular tickets will be highlighted to attendees
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <Separator />

              <div>
                <Label className="text-sm font-medium">Ticket Features</Label>
                <p className="text-xs text-muted-foreground mb-3">
                  Add features or benefits included with this ticket type
                </p>
                
                <div className="space-y-2">
                  {watch(`${name}.${index}.ticketType.features`)?.map((feature: string, featureIndex: number) => (
                    <div key={featureIndex} className="flex items-center gap-2">
                      <Badge variant="secondary" className="flex items-center gap-1">
                        {feature}
                        <button
                          type="button"
                          onClick={() => removeFeature(index, featureIndex)}
                          className="ml-1 hover:text-destructive"
                        >
                          ×
                        </button>
                      </Badge>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add a feature..."
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          const target = e.target as HTMLInputElement
                          addFeature(index, target.value)
                          target.value = ''
                        }
                      }}
                      className="text-sm"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        const input = e.currentTarget.previousElementSibling as HTMLInputElement
                        addFeature(index, input.value)
                        input.value = ''
                      }}
                    >
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
