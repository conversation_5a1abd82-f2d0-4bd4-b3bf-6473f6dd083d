// Database operations types

export interface DatabaseConfig {
  url: string
  serviceKey: string
  anonKey: string
}

export interface QueryOptions {
  limit?: number
  offset?: number
  orderBy?: string
  ascending?: boolean
}

export interface PaginationResult<T> {
  data: T[]
  total: number
  hasMore: boolean
  page: number
  limit: number
}

export interface DashboardStats {
  totalEvents: number
  totalRegistrations: number
  totalRevenue: number
  totalWithdrawals: number
  attendanceRate: number
}

export interface EventWithDetails {
  id: string
  title: string
  slug: string
  description: string
  location: string
  start_date: string
  end_date: string
  images?: any[]
  price: number
  is_published: boolean
  registrations_count: number
  organizer: {
    id: string
    name: string
  } | null
  category: {
    id: string
    name: string
    color: string
    icon: string | null
  } | null
}

export interface UserWithRole {
  id: string
  email: string
  full_name: string
  role_id: string
  created_at: string
  updated_at: string
  email_verified: boolean
  user_roles: {
    id: string
    role_name: string
    description: string
  }
}

export interface RegistrationWithDetails {
  id: string
  event_id: string
  user_id: string
  registration_date: string
  payment_status: string
  total_amount: number
  participant_data: any
  event: {
    id: string
    title: string
    start_date: string
    location: string
  }
  user: {
    id: string
    full_name: string
    email: string
  }
}

export interface OrganizationWithStats {
  id: string
  name: string
  description: string
  website: string
  created_at: string
  events_count: number
  total_registrations: number
  total_revenue: number
}

export interface CertificateWithDetails {
  id: string
  event_id: string
  registration_id: string
  template_id: string
  participant_name: string
  issued_at: string
  certificate_url: string
  verification_code: string
  event: {
    id: string
    title: string
  }
  template: {
    id: string
    name: string
  }
}

// Table names enum for type safety
export enum TableNames {
  USERS = 'users',
  USER_ROLES = 'user_roles',
  EVENTS = 'events',
  REGISTRATIONS = 'registrations',
  ORGANIZATIONS = 'organizations',
  CERTIFICATES = 'certificates',
  ACTIVITY_LOGS = 'activity_logs',
  PAYMENT_GATEWAY_SETTINGS = 'payment_gateway_settings',
  SUBSCRIPTION_PLANS = 'subscription_plans',
  USER_SUBSCRIPTIONS = 'user_subscriptions',
  CATEGORIES = 'categories',
  WEBHOOKS = 'webhooks',
  SYSTEM_SETTINGS = 'system_settings'
}

// Common filter types
export interface EventFilters {
  userId?: string
  organizationId?: string
  categoryId?: string
  isPublished?: boolean
  startDate?: string
  endDate?: string
  searchTerm?: string
}

export interface UserFilters {
  roleId?: string
  emailVerified?: boolean
  searchTerm?: string
  organizationId?: string
}

export interface RegistrationFilters {
  eventId?: string
  userId?: string
  paymentStatus?: string
  startDate?: string
  endDate?: string
}

// Database operation result types
export interface DatabaseResult<T> {
  data: T | null
  error: string | null
  success: boolean
}

export interface DatabaseListResult<T> {
  data: T[]
  error: string | null
  success: boolean
  total?: number
}

// Migration types
export interface Migration {
  id: string
  name: string
  sql: string
  applied_at?: string
}

export interface MigrationResult {
  success: boolean
  error?: string
  migrationsApplied: number
}
