"use client"

import Link from "next/link"
import {
  Calendar,
  Clock,
  MapPin,
  DollarSign,
  Building2,
  Tag,
  Users,
  FileText,
  Activity,
  QrCode
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { formatCurrency, getEventInitials, generateEventGradient } from "@/lib/utils"
import { EventImageCarousel } from "@/components/event-image-carousel"

interface OverviewTabProps {
  event: any
  organization: any
  category: any
  analytics: any
  eventSlug: string
}

export function OverviewTab({
  event,
  organization,
  category,
  analytics,
  eventSlug
}: OverviewTabProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }
  return (
    <div className="space-y-6">
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Registrations */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Registrations</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalRegistrations || 0}</div>
            <p className="text-xs text-muted-foreground">
              {analytics?.totalRegistrations > 0 ? `${analytics?.conversionRate?.toFixed(1) || 0}% conversion rate` : 'No registrations yet'}
            </p>
          </CardContent>
        </Card>

        {/* Paid Registrations */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Registrations</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.paidRegistrations || 0}</div>
            <p className="text-xs text-muted-foreground">
              {analytics?.totalRegistrations > 0 ? `${((analytics?.paidRegistrations || 0) / analytics?.totalRegistrations * 100).toFixed(1)}% payment rate` : 'No payments yet'}
            </p>
          </CardContent>
        </Card>

        {/* Total Revenue */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.totalRevenue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {analytics?.paidRegistrations || 0} paid registrations
            </p>
          </CardContent>
        </Card>

        {/* Attendance Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attendance</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.checkedInCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              {analytics?.totalRegistrations > 0 ? `${analytics?.attendanceRate?.toFixed(1) || 0}% attendance rate` : 'No check-ins yet'}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Event Image and Details */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Event Images */}
              <div className="h-64 md:h-80 w-full">
                {(event?.images && event.images.length > 0) ? (
                  <EventImageCarousel
                    images={event.images}
                    eventTitle={event?.title || "Event"}
                    className="h-full"
                    fixedHeight={true}
                  />
                ) : (
                  <div className="relative h-full w-full bg-gray-200 rounded-lg overflow-hidden">
                    {(() => {
                      const gradient = generateEventGradient(event?.title || "Event")
                      return (
                        <div
                          className={`w-full h-full ${gradient.className} flex items-center justify-center`}
                          style={gradient.style}
                        >
                          <span className="text-white font-bold text-3xl md:text-4xl">
                            {getEventInitials(event?.title || "Event")}
                          </span>
                        </div>
                      )
                    })()}
                  </div>
                )}
              </div>

              {/* Event Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Event Information</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Calendar className="h-5 w-5 mr-3 text-muted-foreground mt-0.5" />
                      <div>
                        <div className="font-medium">
                          {formatDate(event?.start_date)}
                        </div>
                        {event?.start_date !== event?.end_date && (
                          <div className="text-sm text-muted-foreground">
                            to {formatDate(event?.end_date)}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 mr-3 text-muted-foreground" />
                      <span>
                        {formatTime(event?.start_date)} - {formatTime(event?.end_date)}
                      </span>
                    </div>
                    <div className="flex items-start">
                      <MapPin className="h-5 w-5 mr-3 text-muted-foreground mt-0.5" />
                      <span>{event?.location || "No location specified"}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Additional Details</h4>
                  <div className="space-y-3">
                    {event?.price !== null && (
                      <div className="flex items-center">
                        <DollarSign className="h-5 w-5 mr-3 text-muted-foreground" />
                        <span className="font-medium">
                          {event.price === 0 ? "Free Event" : formatCurrency(event.price)}
                        </span>
                      </div>
                    )}
                    {organization && (
                      <div className="flex items-center">
                        <Building2 className="h-5 w-5 mr-3 text-muted-foreground" />
                        <span>{organization.name}</span>
                      </div>
                    )}
                    {category && (
                      <div className="flex items-center">
                        <Tag className="h-5 w-5 mr-3 text-muted-foreground" />
                        <span>{category.name}</span>
                      </div>
                    )}
                    {event?.max_participants && (
                      <div className="flex items-center">
                        <Users className="h-5 w-5 mr-3 text-muted-foreground" />
                        <span>Max {event.max_participants} participants</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              {(event?.description_html || event?.short_description) && (
                <div className="space-y-2">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Description</h4>
                  <div className="text-muted-foreground leading-relaxed">
                    {event.description_html ? (
                      <div dangerouslySetInnerHTML={{ __html: event.description_html }} />
                    ) : (
                      <p>{event.short_description}</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Registration Status and Actions */}
        <div className="space-y-6">
          {/* Registration Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Registration Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Registered</span>
                  <span className="font-bold text-lg">
                    {analytics?.totalRegistrations || 0}
                    {event?.max_participants && (
                      <span className="text-muted-foreground font-normal"> / {event.max_participants}</span>
                    )}
                  </span>
                </div>
                {event?.max_participants && (
                  <Progress
                    value={Math.min(
                      Math.round(((analytics?.totalRegistrations || 0) / event.max_participants) * 100),
                      100,
                    )}
                    className="w-full"
                  />
                )}
                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{analytics?.paidRegistrations || 0}</div>
                    <div className="text-xs text-green-600 font-medium">Paid</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{analytics?.checkedInCount || 0}</div>
                    <div className="text-xs text-blue-600 font-medium">Checked In</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid gap-2">
                <Link href={`/dashboard/events/${eventSlug}/attendance`}>
                  <Button variant="outline" className="w-full justify-start">
                    <QrCode className="mr-2 h-4 w-4" />
                    QR Attendance Scanner
                  </Button>
                </Link>
                <Link href={`/dashboard/events/${eventSlug}/edit`}>
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="mr-2 h-4 w-4" />
                    Edit Event
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  <Activity className="mr-2 h-4 w-4" />
                  View Activity Log
                </Button>
              </div>
              <div className="pt-2 text-xs text-muted-foreground">
                Use the tabs above to manage attendees and certificates.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
